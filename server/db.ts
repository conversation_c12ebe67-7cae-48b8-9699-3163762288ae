import { Pool } from 'pg';
import { drizzle } from 'drizzle-orm/node-postgres';
import dotenv from 'dotenv';
import * as schema from '@shared/schema';

dotenv.config();

const pool = new Pool({
  host: process.env.DB_HOST || 'localhost',
  port: parseInt(process.env.DB_PORT || '5432'),
  user: process.env.DB_USER || 'rasa_user',
  password: process.env.DB_PASSWORD || 'rasa_local_pass',
  database: process.env.DB_NAME || 'rasa_local_db',
  ssl: process.env.DB_SSL === 'true' ? true : false,
});

pool.connect()
  .then(() => console.log('Database connection successfulllll'))
  .catch((err) => console.error('Database connection failed:', err));

export const db = drizzle(pool, { schema });
