// drizzle.config.ts
import type { Config } from 'drizzle-kit';
import dotenv from 'dotenv';

dotenv.config();

export default {
  schema: './shared/schema.ts',
  out: './drizzle',
  dialect: 'postgresql',
  dbCredentials: {
    host: process.env.DB_HOST || 'localhost',
    port: parseInt(process.env.DB_PORT || '5432'),
    user: process.env.DB_USER || 'rasa_user',
    password: process.env.DB_PASSWORD || 'rasa_local_pass',
    database: process.env.DB_NAME || 'rasa_local_db',
    ssl: process.env.DB_SSL === 'true' ? true : false,
  },
} satisfies Config;
