# 🗄️ Local PostgreSQL Database Setup Guide

This guide will help you set up a local PostgreSQL database for your Rasa ChatBot project.

## 📋 Prerequisites

- PostgreSQL is installed and running (✅ Already confirmed)
- You have sudo access to create databases

## 🚀 Quick Setup

### Option 1: Automated Setup (Recommended)
Run the setup script:
```bash
cd RasaChatBot
./setup-local-db.sh
```

### Option 2: Manual Setup
If the automated script doesn't work, follow these steps:

1. **Create the database and user:**
```bash
sudo -u postgres psql -f setup-db-manual.sql
```

2. **Copy environment configuration:**
```bash
cp .env.local .env
```

3. **Install dependencies (if not already done):**
```bash
npm install
```

4. **Push database schema:**
```bash
npm run db:push
```

## 🔧 Configuration Details

### Database Credentials
- **Host:** localhost
- **Port:** 5432
- **Database:** rasa_local_db
- **User:** rasa_user
- **Password:** rasa_local_pass

### Environment Variables
The following environment variables are configured in `.env.local`:
```env
DB_HOST=localhost
DB_PORT=5432
DB_USER=rasa_user
DB_PASSWORD=rasa_local_pass
DB_NAME=rasa_local_db
DB_SSL=false
```

## 📊 Database Schema

Your database will include these tables:
- `users` - User accounts and profiles
- `chats` - Chat conversations
- `messages` - Individual chat messages
- `sessions` - Session storage (required for Replit Auth)
- `embeddings` - Vector embeddings for AI responses
- `user_context` - User context across chats
- `relationships` - Entity relationships
- `psychology_questions` - Psychology assessment questions
- `user_generated_questions` - User-specific questions

## 🛠️ Available Commands

```bash
# Push schema to database
npm run db:push

# Generate migrations
npm run db:generate

# Run migrations
npm run db:migrate

# Open Drizzle Studio (database GUI)
npm run db:studio

# Show setup instructions
npm run db:setup
```

## 🔍 Testing the Connection

Test your database connection:
```bash
PGPASSWORD=rasa_local_pass psql -h localhost -p 5432 -U rasa_user -d rasa_local_db -c "SELECT version();"
```

## 🚨 Troubleshooting

### Connection Issues
If you get connection errors:
1. Ensure PostgreSQL is running: `pg_isready`
2. Check if the database exists: `sudo -u postgres psql -l | grep rasa_local_db`
3. Verify user exists: `sudo -u postgres psql -c "\du" | grep rasa_user`

### Permission Issues
If you get permission errors:
```bash
sudo -u postgres psql -c "GRANT ALL PRIVILEGES ON DATABASE rasa_local_db TO rasa_user;"
```

### Reset Database
To start fresh:
```bash
sudo -u postgres psql -c "DROP DATABASE IF EXISTS rasa_local_db;"
sudo -u postgres psql -c "DROP USER IF EXISTS rasa_user;"
# Then run setup again
```

## 🔄 Switching from Remote to Local

Your configuration files have been updated to use environment variables. To switch between remote and local databases:

1. **For Local:** Use `.env` with local settings
2. **For Remote:** Update `.env` with remote credentials

## 🎯 Next Steps

1. Run the setup (Option 1 or 2 above)
2. Copy `.env.local` to `.env`
3. Run `npm run db:push` to create tables
4. Start your application with `npm run dev`

## 📝 Notes

- The pgvector extension is installed for embedding support
- All sensitive data is stored in environment variables
- The setup preserves your existing schema and relationships
- You can use Drizzle Studio (`npm run db:studio`) to manage your data visually
