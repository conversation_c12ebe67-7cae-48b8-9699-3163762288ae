-- Create all tables for <PERSON><PERSON> Chat<PERSON>ot
-- Run this after setting up the database: psql -U rasa_user -d rasa_local_db -f create-tables.sql

-- Session storage table (required for Replit Auth)
CREATE TABLE IF NOT EXISTS sessions (
    sid VARCHAR PRIMARY KEY,
    sess JSONB NOT NULL,
    expire TIMESTAMP NOT NULL
);

CREATE INDEX IF NOT EXISTS "IDX_session_expire" ON sessions(expire);

-- Users table
CREATE TABLE IF NOT EXISTS users (
    id VARCHAR PRIMARY KEY DEFAULT gen_random_uuid(),
    email VARCHAR UNIQUE NOT NULL,
    password VARCHAR NOT NULL,
    first_name <PERSON><PERSON><PERSON><PERSON>,
    last_name <PERSON><PERSON><PERSON><PERSON>,
    profile_image_url VARCHAR,
    created_at TIMESTAMP DEFAULT NOW(),
    updated_at TIMESTAMP DEFAULT NOW()
);

-- Chats table
CREATE TABLE IF NOT EXISTS chats (
    id SERIAL PRIMARY KEY,
    user_id VARCHAR NOT NULL REFERENCES users(id),
    title VARCHAR NOT NULL,
    created_at TIMESTAMP DEFAULT NOW(),
    updated_at TIMESTAMP DEFAULT NOW()
);

-- Messages table
CREATE TABLE IF NOT EXISTS messages (
    id SERIAL PRIMARY KEY,
    chat_id INTEGER NOT NULL REFERENCES chats(id),
    user_id VARCHAR NOT NULL REFERENCES users(id),
    content TEXT NOT NULL,
    role VARCHAR NOT NULL,
    created_at TIMESTAMP DEFAULT NOW()
);

-- Embeddings table (using TEXT instead of vector for compatibility)
CREATE TABLE IF NOT EXISTS embeddings (
    id SERIAL PRIMARY KEY,
    user_input TEXT NOT NULL,
    bot_output TEXT NOT NULL,
    embedding TEXT NOT NULL, -- JSON string representation
    created_at TIMESTAMP DEFAULT NOW(),
    user_id VARCHAR NOT NULL REFERENCES users(id)
);

-- User context table
CREATE TABLE IF NOT EXISTS user_context (
    id SERIAL PRIMARY KEY,
    user_id VARCHAR NOT NULL REFERENCES users(id),
    context_key VARCHAR NOT NULL,
    context_value TEXT NOT NULL,
    created_at TIMESTAMP DEFAULT NOW(),
    updated_at TIMESTAMP DEFAULT NOW()
);

-- Relationships table
CREATE TABLE IF NOT EXISTS relationships (
    id SERIAL PRIMARY KEY,
    user_id VARCHAR NOT NULL REFERENCES users(id),
    entity1 VARCHAR NOT NULL,
    relationship VARCHAR NOT NULL,
    entity2 VARCHAR NOT NULL,
    created_at TIMESTAMP DEFAULT NOW(),
    updated_at TIMESTAMP DEFAULT NOW()
);

-- Create indexes for relationships
CREATE INDEX IF NOT EXISTS idx_relationships_user_id ON relationships(user_id);
CREATE INDEX IF NOT EXISTS idx_relationships_entity1 ON relationships(entity1);
CREATE INDEX IF NOT EXISTS idx_relationships_entity2 ON relationships(entity2);

-- Psychology questions table
CREATE TABLE IF NOT EXISTS psychology_questions (
    id SERIAL PRIMARY KEY,
    question TEXT NOT NULL,
    category VARCHAR NOT NULL,
    is_active BOOLEAN DEFAULT true,
    order_index INTEGER DEFAULT 0,
    created_at TIMESTAMP DEFAULT NOW(),
    updated_at TIMESTAMP DEFAULT NOW()
);

-- Create indexes for psychology questions
CREATE INDEX IF NOT EXISTS idx_psychology_questions_category ON psychology_questions(category);
CREATE INDEX IF NOT EXISTS idx_psychology_questions_active ON psychology_questions(is_active);

-- User generated questions table
CREATE TABLE IF NOT EXISTS user_generated_questions (
    id SERIAL PRIMARY KEY,
    user_id VARCHAR NOT NULL REFERENCES users(id),
    question TEXT NOT NULL,
    category VARCHAR NOT NULL,
    is_used BOOLEAN DEFAULT false,
    used_at TIMESTAMP,
    created_at TIMESTAMP DEFAULT NOW()
);

-- Create indexes for user generated questions
CREATE INDEX IF NOT EXISTS idx_user_generated_questions_user_id ON user_generated_questions(user_id);
CREATE INDEX IF NOT EXISTS idx_user_generated_questions_category ON user_generated_questions(category);
CREATE INDEX IF NOT EXISTS idx_user_generated_questions_used ON user_generated_questions(is_used);

-- Show success message
SELECT 'All tables created successfully!' as status;
