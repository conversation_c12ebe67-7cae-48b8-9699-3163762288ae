// Test database connection
import { Pool } from 'pg';
import dotenv from 'dotenv';

dotenv.config();

const pool = new Pool({
  host: process.env.DB_HOST || 'localhost',
  port: parseInt(process.env.DB_PORT || '5432'),
  user: process.env.DB_USER || 'rasa_user',
  password: process.env.DB_PASSWORD || 'rasa_local_pass',
  database: process.env.DB_NAME || 'rasa_local_db',
  ssl: process.env.DB_SSL === 'true' ? true : false,
});

async function testConnection() {
  try {
    console.log('🔍 Testing database connection...');
    
    // Test basic connection
    const client = await pool.connect();
    console.log('✅ Database connection successful!');
    
    // Test table existence
    const result = await client.query(`
      SELECT table_name 
      FROM information_schema.tables 
      WHERE table_schema = 'public' 
      ORDER BY table_name;
    `);
    
    console.log('📊 Available tables:');
    result.rows.forEach(row => {
      console.log(`  - ${row.table_name}`);
    });
    
    // Test a simple query
    const userCount = await client.query('SELECT COUNT(*) FROM users');
    console.log(`👥 Current user count: ${userCount.rows[0].count}`);
    
    client.release();
    console.log('🎉 Database test completed successfully!');
    
  } catch (error) {
    console.error('❌ Database connection failed:', error.message);
    console.error('Configuration:', {
      host: process.env.DB_HOST || 'localhost',
      port: process.env.DB_PORT || '5432',
      user: process.env.DB_USER || 'rasa_user',
      database: process.env.DB_NAME || 'rasa_local_db',
    });
  } finally {
    await pool.end();
  }
}

testConnection();
